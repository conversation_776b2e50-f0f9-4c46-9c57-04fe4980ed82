#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用任务发送框架

提供一个可扩展的任务发送器，支持发送不同类型的任务到RabbitMQ队列
"""

import pika
import json
import time
import hashlib
import random
import logging
from datetime import datetime
from typing import Dict, Any, Optional, List
from abc import ABC, abstractmethod

from tests.e2e.config.config import default_config


class TaskSenderBase(ABC):
    """任务发送器基类"""
    
    def __init__(self, config=None):
        """
        初始化任务发送器
        
        Args:
            config: 配置对象，默认使用default_config
        """
        self.config = config or default_config
        self.rabbitmq_config = self.config.rabbitmq_config
        self.queue_config = self.config.queue_config
        self.logger = self._setup_logger()
        
    def _setup_logger(self):
        """设置日志记录器"""
        logger = logging.getLogger(f"{self.__class__.__name__}")
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            
        return logger
    
    def _create_connection(self):
        """创建RabbitMQ连接"""
        credentials = pika.PlainCredentials(
            self.rabbitmq_config['username'],
            self.rabbitmq_config['password']
        )
        
        connection = pika.BlockingConnection(
            pika.ConnectionParameters(
                host=self.rabbitmq_config['host'],
                port=self.rabbitmq_config['port'],
                virtual_host=self.rabbitmq_config['virtual_host'],
                credentials=credentials,
                heartbeat=self.rabbitmq_config['heartbeat']
            )
        )
        
        return connection
    
    def _declare_queue(self, channel, queue_name: str, **kwargs):
        """声明队列"""
        # 使用与现有系统一致的死信配置
        default_args = {
            'durable': True,
            'arguments': {
                'x-dead-letter-exchange': 'dlx.direct',
                'x-dead-letter-routing-key': 'image_convert.dlq'
            }
        }
        default_args.update(kwargs)

        channel.queue_declare(queue=queue_name, **default_args)
    
    @abstractmethod
    def create_task_data(self, **kwargs) -> Dict[str, Any]:
        """
        创建任务数据
        
        Returns:
            Dict[str, Any]: 任务数据字典
        """
        pass
    
    @abstractmethod
    def get_queue_name(self) -> str:
        """
        获取队列名称
        
        Returns:
            str: 队列名称
        """
        pass
    
    def send_task(self, task_data: Optional[Dict[str, Any]] = None, **kwargs) -> bool:
        """
        发送单个任务
        
        Args:
            task_data: 任务数据，如果为None则调用create_task_data创建
            **kwargs: 传递给create_task_data的参数
            
        Returns:
            bool: 发送是否成功
        """
        try:
            if task_data is None:
                task_data = self.create_task_data(**kwargs)
            
            connection = self._create_connection()
            channel = connection.channel()
            
            queue_name = self.get_queue_name()
            self._declare_queue(channel, queue_name)
            
            message_body = json.dumps(task_data, ensure_ascii=False, indent=2)
            
            channel.basic_publish(
                exchange='',
                routing_key=queue_name,
                body=message_body,
                properties=pika.BasicProperties(
                    delivery_mode=2,  # 持久化消息
                    content_type='application/json',
                    content_encoding='utf-8',
                    timestamp=int(time.time())
                )
            )
            
            self.logger.info(f"✅ 成功发送任务到队列 {queue_name}")
            self.logger.info(f"📋 任务ID: {task_data.get('taskId', 'N/A')}")
            
            connection.close()
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 发送任务失败: {e}")
            return False
    
    def send_multiple_tasks(self, count: int, **kwargs) -> List[bool]:
        """
        发送多个任务
        
        Args:
            count: 任务数量
            **kwargs: 传递给create_task_data的参数
            
        Returns:
            List[bool]: 每个任务的发送结果
        """
        results = []
        
        self.logger.info(f"🚀 开始发送 {count} 个任务...")
        
        for i in range(count):
            self.logger.info(f"📤 发送第 {i+1} 个任务...")
            
            # 为批量任务添加批次信息
            batch_kwargs = kwargs.copy()
            batch_kwargs.update({
                'batch_id': i + 1,
                'total_tasks': count,
                'batch_test': True
            })
            
            result = self.send_task(**batch_kwargs)
            results.append(result)
            
            if result:
                self.logger.info(f"   ✅ 任务 {i+1} 发送成功")
            else:
                self.logger.error(f"   ❌ 任务 {i+1} 发送失败")
            
            # 任务间隔
            if i < count - 1:
                time.sleep(1)
        
        success_count = sum(results)
        self.logger.info(f"🎉 批量任务发送完成: {success_count}/{count} 成功")
        
        return results


class ImageTaskSender(TaskSenderBase):
    """图像任务发送器"""

    # 图像类型配置映射
    IMAGE_TYPE_CONFIGS = {
        1: {  # 普通图像
            'name': '普通图像',
            'extensions': ['.png', '.jpg', '.jpeg'],
            'default_urls': [
                '/medical-data/samples/normal/sample_image_001.png',
                '/medical-data/samples/normal/sample_image_002.jpg',
                '/medical-data/samples/normal/test_normal.jpeg'
            ],
            'description': '常见的自然图像格式，支持PNG、JPG、JPEG'
        },
        2: {  # DICOM图像
            'name': 'DICOM图像',
            'extensions': ['.dcm', '.dicom'],
            'default_urls': [
                '/medical-data/samples/dicom/sample_ct_001.dcm',
                '/medical-data/samples/dicom/sample_mri_002.dcm',
                '/medical-data/samples/dicom/test_dicom.dicom'
            ],
            'description': 'DICOM医学影像标准格式'
        },
        3: {  # 病理图像
            'name': '病理图像',
            'extensions': ['.svs', '.tif', '.tiff', '.kfb'],
            'default_urls': [
                '/medical-data/data/liver/WSI/ZheEr/浙二/2022-10-18/201137807.svs',
                '/medical-data/samples/pathology/sample_pathology_001.svs',
                '/medical-data/samples/pathology/sample_slide_002.tif',
                '/medical-data/samples/pathology/test_kfb.kfb'
            ],
            'description': '数字病理切片图像，支持SVS、TIF、KFB等格式'
        },
        4: {  # 多通道图像
            'name': '多通道图像',
            'extensions': ['.qptiff', '.tiff'],
            'default_urls': [
                '/medical-data/samples/multichannel/sample_qptiff_001.qptiff',
                '/medical-data/samples/multichannel/sample_multichannel_002.tiff',
                '/medical-data/samples/multichannel/test_fluorescence.qptiff'
            ],
            'description': '量化病理图像和多重荧光图像'
        }
    }

    def get_queue_name(self) -> str:
        """获取图像转换队列名称"""
        return self.queue_config['image_convert_queue']
    
    def create_task_data(self, **kwargs) -> Dict[str, Any]:
        """
        创建图像任务数据

        Args:
            **kwargs: 任务参数
                - image_type_id: 图像类型ID (1=普通, 2=DICOM, 3=病理, 4=多通道)
                - project_id: 项目ID (默认: '17')
                - image_url: 图像文件路径 (可选，会根据image_type_id自动选择合适的默认路径)
                - image_name: 图像名称 (可选，默认生成随机hash)
                - url_index: 指定使用第几个默认URL (可选，默认0)
                - metadata: 元数据 (可选)
                - batch_id: 批次ID (内部使用)
                - total_tasks: 总任务数 (内部使用)
                - batch_test: 是否为批量测试 (内部使用)

        Returns:
            Dict[str, Any]: 图像任务数据

        Raises:
            ValueError: 当image_type_id不支持时抛出异常
        """
        # 生成随机hash作为默认图像名称
        random_string = f"{time.time()}-{random.randint(1000, 9999)}"
        default_image_name = hashlib.md5(random_string.encode()).hexdigest()[:16]

        # 获取参数
        image_type_id = kwargs.get('image_type_id', 3)  # 默认病理图
        project_id = kwargs.get('project_id', '17')
        image_name = kwargs.get('image_name', default_image_name)
        url_index = kwargs.get('url_index', 0)  # 默认使用第一个URL

        # 根据图像类型获取默认URL
        image_url = self._get_default_image_url(image_type_id, kwargs.get('image_url'), url_index)
        
        # 批次信息
        batch_id = kwargs.get('batch_id')
        total_tasks = kwargs.get('total_tasks')
        batch_test = kwargs.get('batch_test', False)
        
        # 构建任务ID
        task_id_parts = ['img-task']
        if batch_test and batch_id:
            task_id_parts.extend(['batch', str(batch_id)])
        task_id_parts.extend([str(int(time.time())), str(random.randint(100, 999))])
        task_id = '-'.join(task_id_parts)
        
        # 构建图像ID
        image_id_parts = ['img']
        if batch_test and batch_id:
            image_id_parts.extend(['batch', str(batch_id)])
        image_id_parts.extend([str(int(time.time())), str(random.randint(1000, 9999))])
        image_id = '-'.join(image_id_parts)
        
        # 基础任务数据
        task_data = {
            "taskId": task_id,
            "imageTypeId": image_type_id,
            "projectId": str(project_id),
            "imageName": image_name,
            "imageId": image_id,
            "imageUrl": image_url,
            "timestamp": datetime.now().isoformat()
        }
        
        # 添加批次信息
        if batch_test:
            task_data.update({
                "batch_id": batch_id,
                "total_tasks": total_tasks
            })
        
        # 添加元数据
        metadata = kwargs.get('metadata', {})
        default_metadata = {
            "test_mode": True,
            "source": "E2E测试",
            "image_type": self._get_image_type_name(image_type_id)
        }
        
        if batch_test:
            default_metadata["batch_test"] = True
            
        default_metadata.update(metadata)
        task_data["metadata"] = default_metadata
        
        return task_data
    
    def _get_default_image_url(self, image_type_id: int, custom_url: Optional[str] = None,
                              url_index: int = 0) -> str:
        """
        获取默认图像URL

        Args:
            image_type_id: 图像类型ID
            custom_url: 自定义URL，如果提供则直接返回
            url_index: 使用第几个默认URL

        Returns:
            str: 图像文件URL

        Raises:
            ValueError: 当image_type_id不支持时抛出异常
        """
        # 如果提供了自定义URL，直接返回
        if custom_url:
            return custom_url

        # 检查图像类型是否支持
        if image_type_id not in self.IMAGE_TYPE_CONFIGS:
            supported_types = list(self.IMAGE_TYPE_CONFIGS.keys())
            raise ValueError(f"不支持的图像类型ID: {image_type_id}，支持的类型: {supported_types}")

        config = self.IMAGE_TYPE_CONFIGS[image_type_id]
        default_urls = config['default_urls']

        # 确保url_index在有效范围内
        if url_index >= len(default_urls):
            self.logger.warning(f"URL索引 {url_index} 超出范围，使用默认索引 0")
            url_index = 0

        selected_url = default_urls[url_index]

        self.logger.debug(f"为图像类型 {image_type_id} ({config['name']}) 选择URL[{url_index}]: {selected_url}")

        return selected_url

    def _get_image_type_name(self, image_type_id: int) -> str:
        """获取图像类型名称"""
        if image_type_id in self.IMAGE_TYPE_CONFIGS:
            return self.IMAGE_TYPE_CONFIGS[image_type_id]['name']
        return f"未知类型({image_type_id})"

    def get_supported_image_types(self) -> Dict[int, Dict[str, Any]]:
        """
        获取支持的图像类型信息

        Returns:
            Dict: 支持的图像类型配置
        """
        return self.IMAGE_TYPE_CONFIGS.copy()

    def get_image_type_info(self, image_type_id: int) -> Optional[Dict[str, Any]]:
        """
        获取特定图像类型的详细信息

        Args:
            image_type_id: 图像类型ID

        Returns:
            Dict: 图像类型信息，如果不存在返回None
        """
        return self.IMAGE_TYPE_CONFIGS.get(image_type_id)

    def list_available_urls(self, image_type_id: int) -> List[str]:
        """
        列出指定图像类型的所有可用URL

        Args:
            image_type_id: 图像类型ID

        Returns:
            List[str]: 可用的URL列表

        Raises:
            ValueError: 当image_type_id不支持时抛出异常
        """
        if image_type_id not in self.IMAGE_TYPE_CONFIGS:
            supported_types = list(self.IMAGE_TYPE_CONFIGS.keys())
            raise ValueError(f"不支持的图像类型ID: {image_type_id}，支持的类型: {supported_types}")

        return self.IMAGE_TYPE_CONFIGS[image_type_id]['default_urls'].copy()

# 便捷函数
def send_image_task(**kwargs) -> bool:
    """
    发送单个图像任务的便捷函数

    Args:
        **kwargs: 传递给ImageTaskSender.send_task的参数

    Returns:
        bool: 发送是否成功

    Example:
        # 发送病理图像任务（默认）
        send_image_task()

        # 发送普通图像任务
        send_image_task(image_type_id=1)

        # 发送DICOM图像任务，使用第二个默认URL
        send_image_task(image_type_id=2, url_index=1)

        # 发送自定义URL的任务
        send_image_task(image_type_id=4, image_url='/custom/path/image.qptiff')
    """
    sender = ImageTaskSender()
    return sender.send_task(**kwargs)

def send_multiple_image_tasks(count: int, **kwargs) -> List[bool]:
    """
    发送多个图像任务的便捷函数

    Args:
        count: 任务数量
        **kwargs: 传递给ImageTaskSender.send_multiple_tasks的参数

    Returns:
        List[bool]: 每个任务的发送结果

    Example:
        # 发送3个病理图像任务
        send_multiple_image_tasks(3, image_type_id=3)

        # 发送5个普通图像任务到项目18
        send_multiple_image_tasks(5, image_type_id=1, project_id='18')
    """
    sender = ImageTaskSender()
    return sender.send_multiple_tasks(count, **kwargs)

def get_supported_image_types() -> Dict[int, Dict[str, Any]]:
    """
    获取支持的图像类型信息的便捷函数

    Returns:
        Dict: 支持的图像类型配置

    Example:
        types = get_supported_image_types()
        for type_id, config in types.items():
            print(f"类型 {type_id}: {config['name']}")
            print(f"  支持格式: {config['extensions']}")
            print(f"  默认URL数量: {len(config['default_urls'])}")
    """
    sender = ImageTaskSender()
    return sender.get_supported_image_types()

def list_available_urls(image_type_id: int) -> List[str]:
    """
    列出指定图像类型的所有可用URL的便捷函数

    Args:
        image_type_id: 图像类型ID

    Returns:
        List[str]: 可用的URL列表

    Example:
        urls = list_available_urls(3)  # 获取病理图像的所有URL
        for i, url in enumerate(urls):
            print(f"URL[{i}]: {url}")
    """
    sender = ImageTaskSender()
    return sender.list_available_urls(image_type_id)
